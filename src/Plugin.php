<?php

namespace ThinkMatic\PluginFramework;

use D<PERSON>\Container;
use DI\ContainerBuilder;
use DI\DependencyException;
use DI\NotFoundException;
use Exception;
use Psr\Container\ContainerInterface;
use ThinkMatic\PluginFramework\Contracts\Invokable;

abstract class Plugin {
	protected static ?self $instance = null;

	/**
	 * Main plugin file path
	 * @var string
	 */
	protected string $file;

	/**
	 * Relative Path for the plugin.
	 * @var string
	 */
	protected string $relativePath;

	/**
	 * Relative URL for the plugin.
	 * @var string
	 */
	protected string $relativeUrl;

	/**
	 * @var ContainerBuilder
	 */
	public ContainerBuilder $builder;

	/**
	 * @var Container
	 */
	protected Container $container;

	/**
	 * Plugin Version
	 * @var string
	 */
	protected string $version;

	/**
	 * Plugin Name
	 * @var string
	 */
	protected string $name;

	/**
	 * Plugin Slug
	 * @var string
	 */
	protected string $slug;

	/**
	 * Config for the plugin.
	 * @var array
	 */
	protected array $pluginConfig = [];

	/**
	 * An array of plugin activation handlers. (Activator, Deactivator, Uninstaller)
	 * @var array
	 */
	protected array $handlers = [];

	/**
	 * An array of plugin service providers. (i.e: Enqueue, Routing, Shortcode etc.)
	 * @var array
	 */
	protected array $providers = [];

	/**
	 * @throws Exception
	 */
	public function __construct( $pluginFile, $containerClass = Container::class ) {
		$this->file         = $pluginFile;
		$this->relativePath = plugin_dir_path( $this->file );
		$this->relativeUrl  = plugin_dir_url( $this->file );
		$this->builder      = new ContainerBuilder( $containerClass );
		$this->pluginConfig = require_once $this->getPath( 'config/plugin.php' );

		$this->version  = $this->pluginConfig['version'] ?? '0.0.1';
		$this->name     = $this->pluginConfig['name'] ?? 'ThinkMatic';
		$this->slug     = $this->pluginConfig['slug'] ?? 'thinkmatic';
		$this->handlers = $this->pluginConfig['handlers'] ?? [];

		// Set the instance if instantiate without getInstance.
		static::$instance = $this;
	}

	/**
	 * @throws Exception
	 */
	public static function getInstance( ?string $file = null ): self {
		if ( self::$instance === null ) {
			if ( $file === null ) {
				$file = __FILE__;
			}

			self::$instance = new static( $file );
		}

		return self::$instance;
	}


	/**
	 * @throws Exception
	 */
	final public function run( ?callable $callback = null ) {
		// Register all the providers.
		$this->providers = require_once $this->getPath( 'config/providers.php' );

		$registeredProviders = [];
		foreach ( $this->providers as $provider ) {
			$_provider             = new $provider( $this );
			$registeredProviders[] = $_provider;
			$_provider->register();
		}

		// Initiate or build the container.
		$this->container = $this->builder->build();

		foreach ( $registeredProviders as $provider ) {
			$provider->boot();
		}

		if ( is_callable( $callback ) ) {
			$callback( $this );
		}

		if ( php_sapi_name() === 'cli' ) {
			return;
		}

		$this->registerPlugins( $this->handlers );
	}

	/**
	 * @throws Exception
	 */
	public function registerPlugins( $handlers = [] ) {
		if ( ! empty( $handlers['activator'] ) ) {
			register_activation_hook( $this->file, $this->resolveHandler( $handlers['activator'] ) );
		}

		if ( ! empty( $handlers['deactivator'] ) ) {
			register_deactivation_hook( $this->file, $this->resolveHandler( $handlers['deactivator'] ) );
		}

		if ( ! empty( $handlers['uninstaller'] ) ) {
			register_uninstall_hook( $this->file, $this->resolveHandler( $handlers['uninstaller'] ) );
		}
	}

	public function bind( string $name, callable $resolver ): void {
		$this->builder->addDefinitions( [
			$name => $resolver
		] );
	}

	public function getPath( string $path = '' ): string {
		return $this->relativePath . ltrim( $path, '/' );
	}

	public function getUrl( string $path = '' ): string {
		return $this->relativeUrl . ltrim( $path, '/' );
	}

	public function getVersion(): string {
		return trim( $this->version );
	}

	public function getName(): string {
		return trim( $this->name );
	}

	public function getSlug(): string {
		return trim( $this->slug );
	}

	public function getTextDomain(): string {
		return trim( $this->pluginConfig['textdomain'] ) ?? $this->getSlug();
	}

	/**
	 * @throws Exception
	 */
	public function getContainer(): ContainerInterface {
		return $this->container;
	}


	public function getConfig( string $key = '' ) {
		if ( empty( $key ) ) {
			return $this->pluginConfig;
		}

		if ( isset( $this->pluginConfig[ $key ] ) ) {
			return $this->pluginConfig[ $key ];
		}

		if ( strpos( $key, '.' ) !== false ) {
			$explode = explode( '.', $key );

			$_value = null;
			foreach ( $explode as $item ) {
				if ( ! is_null( $_value ) ) {
					$_value = $_value[ $item ] ?? null;
				} else {
					$_value = $this->pluginConfig[ $item ];
				}
			}

			return $_value;
		}

		return null;
	}

	public function resolve( string $name ) {
		try {
			return $this->container->get( $name );
		} catch ( NotFoundException|DependencyException $e ) {
			die( $e->getMessage() );
		}
	}

	/**
	 * @throws Exception
	 */
	public function resolveHandler( string $handler ): Invokable {
		$handler = $this->resolve( $handler );
		if ( ! $handler instanceof Invokable ) {
			throw new Exception( 'Handler must be an instance of InvokableContract' );
		}

		return $handler;
	}
}
