<?php

namespace ThinkMatic\Database;

// Ensure helpers are loaded
require_once __DIR__ . '/helpers.php';

use Exception;
use ThinkMatic\Database\Builder\Model;
use ThinkMatic\Database\Migration\Migrator;
use ThinkMatic\Database\Query\Builder;
use ThinkMatic\PluginFramework\ServiceProvider;

class Provider extends ServiceProvider {
	/**
	 * @throws Exception
	 */
	public function register(): void {
		global $wpdb;
		$connection = new Connection( $wpdb, [ 'prefix' => $wpdb->prefix ] );

		Model::setConnection( $connection );

		$this->plugin->bind( Builder::class, function () use ( $connection ) {
			return new Builder( $connection );
		} );

		$this->plugin->bind( Migrator::class, function () {
			return new Migrator( $this->plugin );
		} );
	}

	public function boot(): void {
		$this->plugin->resolve( Migrator::class )->initialize();
	}
}
