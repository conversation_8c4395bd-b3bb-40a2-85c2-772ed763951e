<?php

namespace ThinkMatic\Database\Builder;

// Ensure helpers are loaded for Model functionality
require_once __DIR__ . '/../helpers.php';

use JsonSerializable;
use ThinkMatic\Database\Builder\Traits\ForwardsCalls;
use ThinkMatic\Database\Builder\Traits\HasAttributes;
use ThinkMatic\Database\Builder\Traits\HasRelations;
use ThinkMatic\Database\Connection;
use ThinkMatic\Database\Query\Builder as QueryBuilder;

/**
 * @mixin Builder
 * @mixin QueryBuilder
 */
abstract class Model implements JsonSerializable {
	use HasAttributes, HasRelations, ForwardsCalls;

	/**
	 * The array of global scopes on the model.
	 *
	 * @var array
	 */
	protected static array $globalScopes = [];

	/**
	 * The array of booted models.
	 *
	 * @var array
	 */
	protected static array $booted = [];

	/**
	 * The array of trait initializers that will be called on each new instance.
	 *
	 * @var array
	 */
	protected static array $traitInitializers = [];

	/**
	 * @var string
	 */
	protected string $table;

	public bool $exists = false;

	/**
	 * @var string
	 */
	protected string $primaryKey = 'ID';

	/**
	 * Store protected properties
	 *
	 * @var array
	 */
	protected array $guarded = [];

	/**
	 * @var class-string<Collection<*, *>>
	 */
	protected static string $collectionClass = Collection::class;

	protected static Connection $connection;

	/**
	 * Handle dynamic property assignment.
	 *
	 * @param string $name
	 * @param mixed  $value
	 */
	public function __set( string $name, $value ) {
		$this->setAttribute( $name, $value );
	}

	/**
	 * Handle dynamic property retrieval.
	 *
	 * @param string $name
	 *
	 * @return mixed
	 */
	public function __get( string $name ) {
		return $this->getAttribute( $name );
	}

	public function __call( $method, $args ) {
		return $this->forwardCallTo( $this->newQuery(), $method, $args );
	}

	/**
	 * Handle dynamic static method calls into the model.
	 *
	 * @param string $method
	 * @param        $args
	 *
	 * @return mixed
	 */
	public static function __callStatic( string $method, $args ) {
		return ( new static )->$method( ...$args );
	}

	public function getPrimaryKey(): string {
		return $this->primaryKey;
	}

	public function getTable(): string {
		$className = basename( str_replace( '\\', '/', get_class( $this ) ) );

		return $this->table ?? strtolower( $className ) . 's';
	}

	public function setTable( $table ): Model {
		$this->table = $table;

		return $this;
	}

	public function getGuarded(): array {
		return $this->guarded;
	}

	public static function setConnection( $connection ) {
		self::$connection = $connection;
	}

	public function newCollection( $items = [] ) {
		return new static::$collectionClass( $items );
	}

	public function newInstance( $attributes = [], $exists = false ): Model {
		$model = new static;
		$model->setTable( $this->getTable() );
		$model->setRawAttributes( (array) $attributes );
		$model->exists = $exists;

		return $model;
	}

	public function newFromBuilder( $attributes = [] ): Model {
		$model = $this->newInstance( $attributes, true );
		$model->setRawAttributes( (array) $attributes );

		return $model;
	}

	private function setRawAttributes( array $attributes ): Model {
		$this->attributes = $attributes;

		return $this;
	}

	public static function all() {
		return static::query()->get();
	}

	public static function with( $relations ): Builder {
		return static::query()->with( $relations );
	}

	public static function query(): Builder {
		return ( new static )->newQuery();
	}

	public function newQuery(): Builder {
		$builder = ( new Builder( $this->newBaseQueryBuilder() ) )->setModel( $this );
		return $this->applyGlobalScopes($builder);
	}

	private function newBaseQueryBuilder(): QueryBuilder {
		return new QueryBuilder( self::$connection );
	}

	public function jsonSerialize(): array {
		return $this->toArray();
	}

	public function toArray(): array {
		return $this->attributes;
	}

	/**
	 * Constructor.
	 */
	public function __construct() {
		$this->bootIfNotBooted();
		$this->initializeTraits();
	}

	/**
	 * Check if the model needs to be booted and if so, do it.
	 *
	 * @return void
	 */
	protected function bootIfNotBooted(): void {
		$class = static::class;

		if (!isset(static::$booted[$class])) {
			static::$booted[$class] = true;
			static::boot();
		}
	}

	/**
	 * The "boot" method of the model.
	 *
	 * @return void
	 */
	protected static function boot(): void {
		// Boot all traits with boot{TraitName} methods
		static::bootTraits();
	}

	/**
	 * Initialize any traits on the model.
	 *
	 * @return void
	 */
	protected function initializeTraits(): void {
		$class = static::class;

		foreach (self::class_uses_recursive($class) as $trait) {
			$method = 'initialize'. self::class_basename($trait);

			if (method_exists($this, $method)) {
				$this->{$method}();
			}
		}
	}

	/**
	 * Boot all of the bootable traits on the model.
	 *
	 * @return void
	 */
	protected static function bootTraits(): void {
		$class = static::class;

		$booted = [];

		foreach (self::class_uses_recursive($class) as $trait) {
			$method = 'boot'.self::class_basename($trait);

			if (method_exists($class, $method) && !in_array($method, $booted)) {
				forward_static_call([$class, $method]);

				$booted[] = $method;
			}
		}
	}

	/**
	 * Register a new global scope on the model.
	 *
	 * @param string $identifier
	 * @param callable $scope
	 * @return void
	 */
	public static function addGlobalScope(string $identifier, callable $scope): void {
		static::$globalScopes[static::class][$identifier] = $scope;
	}

	/**
	 * Remove a global scope from the model.
	 *
	 * @param string $identifier
	 * @return void
	 */
	public static function removeGlobalScope(string $identifier): void {
		unset(static::$globalScopes[static::class][$identifier]);
	}

	/**
	 * Get the global scopes for this model.
	 *
	 * @return array
	 */
	public function getGlobalScopes(): array {
		return static::$globalScopes[static::class] ?? [];
	}

	/**
	 * Apply all the global scopes to a builder instance.
	 *
	 * @param Builder $builder
	 * @return Builder
	 */
	public function applyGlobalScopes(Builder $builder): Builder {
		foreach ($this->getGlobalScopes() as $identifier => $scope) {
			$scope($builder);
		}

		return $builder;
	}

	/**
	 * Create a new model instance without global scopes.
	 *
	 * @return static
	 */
	public static function withoutGlobalScopes() {
		$instance = new static;
		return $instance->newQueryWithoutScopes();
	}

	/**
	 * Get a new query builder without a given scope.
	 *
	 * @param string $scope
	 * @return Builder
	 */
	public function withoutGlobalScope(string $scope): Builder {
		return $this->newQuery()->withoutGlobalScope($scope);
	}

	/**
	 * Get a new query builder with no scopes.
	 *
	 * @return Builder
	 */
	public function newQueryWithoutScopes(): Builder {
		return $this->newQuery()->withoutGlobalScopes();
	}

	/**
	 * Get all traits used by all parent classes.
	 *
	 * @param string $class
	 * @return array
	 */
	protected static function class_uses_recursive(string $class): array {
		$results = [];

		foreach (array_reverse(class_parents($class)) + [$class => $class] as $class) {
			$results += trait_uses_recursive($class);
		}

		return array_unique($results);
	}

	/**
	 * Get the class "basename" of the given object / class.
	 *
	 * @param string|object $class
	 * @return string
	 */
	protected static function class_basename($class): string {
		$class = is_object($class) ? get_class($class) : $class;

		return basename(str_replace('\\', '/', $class));
	}
}
