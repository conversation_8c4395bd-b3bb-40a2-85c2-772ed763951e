<?php

use ThinkMatic\Database\Migration\Migration;
use ThinkMatic\Database\Migration\Statement;

class V5_CreateSeatsTable extends Migration {
	public function up(): Statement {
		return $this->createTable(
			'seats',
			[
				$this->id(),
				$this->foreign( 'service_id', 'services' ),
				$this->foreign( 'hall_id', 'halls' ),
				$this->foreign( 'seat_category_id', 'seat_categories' ),
				$this->string( 'number' ),
				$this->float( 'price' ),
				$this->boolean( 'status' ),
				$this->timestamps(),
				$this->softDeletes(),
				$this->index('service_id'),
				$this->index('hall_id'),
				$this->index('seat_category_id'),
				$this->index('number'),
				$this->index('status'),
				$this->compositeIndex(['hall_id', 'number'], 'hall_seat_number_index')
			]
		);
	}

	public function down(): Statement {
		return $this->dropTable( 'seats' );
	}
}
