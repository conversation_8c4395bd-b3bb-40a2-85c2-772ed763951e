{"name": "thinkmatic/schedopia", "type": "project", "license": "GPLv2", "authors": [{"name": "ThinkMatic"}], "autoload": {"psr-4": {"ThinkMatic\\Schedopia\\": "includes/"}, "files": ["vendor/thinkmatic/database/src/helpers.php"]}, "require": {"php": ">=7.4", "thinkmatic/plugin-framework": "dev-priyo", "thinkmatic/database": "dev-priyo", "ext-pdo": "*", "ext-json": "*"}, "repositories": [{"type": "vcs", "url": "**************:thinkmatic/plugin-framework.git"}, {"type": "vcs", "url": "**************:thinkmatic/database.git"}], "autoload-dev": {"psr-4": {"ThinkMatic\\Schedopia\\Tests\\": "tests/"}}, "require-dev": {"squizlabs/php_codesniffer": "~3.11.1", "wp-coding-standards/wpcs": "~3.1.0", "phpcompatibility/phpcompatibility-wp": "~2.1.5", "yoast/phpunit-polyfills": "^1.1.0", "wp-phpunit/wp-phpunit": "^6.6"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"test": "phpunit", "format": "@php ./bin/phpcbf", "lint": "@php ./vendor/bin/phpcs --report=summary,code,source"}}