# ThinkMatic\Database

A wrapper for $WPDB Query Builder. It will work as a Service Provider for ThinkMatic\PluginFramework.


# How To Use?

To use this package, you must use ThinkMatic\PluginFramework also in order use it easily.

Just include the `ThinkMatic\Database\Provider::class` in your `provider.php`. Your database service is ready to use in your other services boot method or in your plugin's codebase.

For more info follow the PluginFramework [Docs](https://github.com/thinkmatic/plugin-framework).
